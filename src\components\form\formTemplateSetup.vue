this.selectedWorkflow = workflow
      this.workflowId = workflow.id
      this.isWorkflowOpen = false
      this.updateStoreData()<!-- Updated FormTemplateSetup.vue with persistent data support -->
<template>
  <div class="template-setup">
    <div class="template-box">
      <h3 v-if="!isEdit" class="title">Create New Form</h3>
      <h2 v-else class="title">Edit Form</h2>
      <p  v-if="!isEdit" class="description">Choose a form type and give your template a name to get started</p>

      <div class="form-group">
        <label for="templateName">Template Name <span class="required-asterisk">*</span></label>
        <input
          id="templateName"
          v-model="templateName"
          type="text"
          placeholder="Enter template name"
          @input="updateStoreData"
        />
      </div>

      <div class="form-group">
        <label for="formType">Form Type <span class="required-asterisk">*</span></label>
        <div class="custom-select" :class="{ 'is-open': isFormTypeOpen, 'disabled': isEdit }">
          <div class="select-trigger" @click="!isEdit && toggleFormType()">
            <span v-if="formTypeselected">{{ formTypeselected.name }}</span>
            <span v-else class="placeholder">Select a form type</span>
            <svg v-if="!isEdit" class="dropdown-icon" :class="{ 'rotated': isFormTypeOpen }" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else class="lock-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              <path d="M12 11V17" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div v-if="isFormTypeOpen && !isEdit" class="dropdown-menu">
            <div
              v-for="type in filteredFormTypeList"
              :key="type.id"
              class="dropdown-item"
              @click="selectFormType(type)"
            >
              <svg class="item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>
                <line x1="9" y1="9" x2="15" y2="9" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="15" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              {{ type.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="sequenceType">Sequence Type</label>
        <div class="custom-select" :class="{ 'is-open': isSequenceTypeOpen }">
          <div class="select-trigger" @click="toggleSequenceType">
            <span v-if="selectedSequenceType">{{ selectedSequenceType.name }}</span>
            <span v-else class="placeholder">Select sequence type</span>
            <svg class="dropdown-icon" :class="{ 'rotated': isSequenceTypeOpen }" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div v-if="isSequenceTypeOpen" class="dropdown-menu">
            <div
              v-for="sequence in sequenceTemplates"
              :key="sequence.id"
              class="dropdown-item"
              @click="selectSequenceType(sequence)"
            >
              <svg class="item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" fill="none"/>
              </svg>
              {{ sequence.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="workflow">Workflow</label>
        <div class="custom-select" :class="{ 'is-open': isWorkflowOpen }">
          <div class="select-trigger" @click="toggleWorkflow">
            <span v-if="selectedWorkflow">{{ selectedWorkflow.name }}</span>
            <span v-else class="placeholder">Select workflow</span>
            <svg class="dropdown-icon" :class="{ 'rotated': isWorkflowOpen }" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div v-if="isWorkflowOpen" class="dropdown-menu">
            <div
              v-for="workflow in workflows"
              :key="workflow.id"
              class="dropdown-item"
              @click="selectWorkflow(workflow)"
            >
              <svg class="item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 16V8A2 2 0 0 0 19 6H5A2 2 0 0 0 3 8V16A2 2 0 0 0 5 18H19A2 2 0 0 0 21 16Z" stroke="currentColor" stroke-width="2" fill="none"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" fill="none"/>
              </svg>
              {{ workflow.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="button-group">
        <button v-if="!isEdit" class="btn btn-black pointer" @click="handleSubmit"
          :disabled="!formTypeselected || !templateName"
        >
          Next
        </button>
        <button v-else class="btn btn-black" @click="handleSubmit"
          :disabled="!formTypeselected || !templateName"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { formTemplateNameCheck } from '@/api'
import { alert } from '@/plugins/notification'
import { mapGetters } from 'vuex'

export default {
  data () {
    return {
      sequenceTypeId: '',
      formTypeselected: '',
      templateName: '',
      sequenceType: '',
      workflowId: '',
      selectedSequenceType: null,
      selectedWorkflow: null,
      isFormTypeOpen: false,
      isSequenceTypeOpen: false,
      isWorkflowOpen: false
    }
  },
  props: {
    editData: {
      type: Object
    },
    workflows: {
      type: Array,
      default: () => []
    },
    sequenceTemplates: {
      type: Array,
      default: () => []
    },
    formType: {
      type: String
    },
    filteredFormTypeList: {
      type: Array,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters('form', ['setupData'])
  },
  watch: {
    // Watch for changes in editData and populate fields
    editData: {
      handler (newData) {
        if (this.isEdit && newData) {
          this.populateEditData()
        }
      },
      immediate: true,
      deep: true
    },
    // Watch for setup data from store
    setupData: {
      handler (newData) {
        if (!this.isEdit && newData && Object.keys(newData).length > 0) {
          this.populateStoreData()
        }
      },
      immediate: true,
      deep: true
    },
    // Watch for changes in arrays to populate data when they load
    sequenceTemplates: {
      handler () {
        if (this.isEdit && this.editData) {
          this.populateEditData()
        } else if (!this.isEdit && this.setupData) {
          this.populateStoreData()
        }
      }
    },
    workflows: {
      handler () {
        if (this.isEdit && this.editData) {
          this.populateEditData()
        } else if (!this.isEdit && this.setupData) {
          this.populateStoreData()
        }
      }
    },
    filteredFormTypeList: {
      handler () {
        if (this.isEdit && this.editData) {
          this.populateEditData()
        } else if (!this.isEdit && this.persistedSetupData) {
          this.populatePersistedData()
        }
      }
    }
  },
  mounted () {
    // Close dropdowns when clicking outside
    document.addEventListener('click', this.handleClickOutside)

    // Populate edit data if editing
    if (this.isEdit && this.editData) {
      this.populateEditData()
    } else if (!this.isEdit && this.setupData) {
      this.populateStoreData()
    }
  },
  beforeUnmount () {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    // Populate data from Vuex store
    populateStoreData () {
      if (!this.setupData) return

      this.templateName = this.setupData.templateName || ''
      this.formTypeselected = this.setupData.formTypeselected || ''
      this.selectedSequenceType = this.setupData.selectedSequenceType || null
      this.selectedWorkflow = this.setupData.selectedWorkflow || null
      this.sequenceTypeId = this.setupData.sequenceTypeId || ''
      this.workflowId = this.setupData.workflowId || ''
    },

    // Update Vuex store with current data
    updateStoreData () {
      this.$store.dispatch('form/updateSetupData', {
        templateName: this.templateName,
        formTypeselected: this.formTypeselected,
        selectedSequenceType: this.selectedSequenceType,
        selectedWorkflow: this.selectedWorkflow,
        sequenceTypeId: this.sequenceTypeId,
        workflowId: this.workflowId
      })
    },

    populateEditData () {
      if (!this.editData) return

      this.templateName = this.editData.name || this.editData.templateName || ''

      const sequenceId = this.editData.core_sequence_id_template?.id || this.editData.sequence_template_id
      if (sequenceId && this.sequenceTemplates.length) {
        this.selectedSequenceType = this.sequenceTemplates.find(
          seq => seq.id === sequenceId
        ) || null
        this.sequenceTypeId = sequenceId
      }

      const workflowId = this.editData.workflow_template?.id || this.editData.workflowId || this.editData.workflow_template_id
      if (workflowId && this.workflows.length) {
        this.selectedWorkflow = this.workflows.find(
          workflow => workflow.id === workflowId
        ) || null
        this.workflowId = workflowId
      }

      if (this.filteredFormTypeList.length) {
        const formTypeData = this.editData.core_form_type || this.editData.formType
        if (formTypeData) {
          if (typeof formTypeData === 'object') {
            this.formTypeselected = this.filteredFormTypeList.find(
              type => type.id === formTypeData.id
            ) || formTypeData
          } else if (typeof formTypeData === 'number') {
            this.formTypeselected = this.filteredFormTypeList.find(
              type => type.id === formTypeData
            ) || null
          }
        }
      }
    },
    toggleFormType () {
      if (this.isEdit) return // Don't allow toggle in edit mode
      this.isFormTypeOpen = !this.isFormTypeOpen
      this.isSequenceTypeOpen = false
      this.isWorkflowOpen = false
    },

    toggleSequenceType () {
      this.isSequenceTypeOpen = !this.isSequenceTypeOpen
      this.isFormTypeOpen = false
      this.isWorkflowOpen = false
    },

    toggleWorkflow () {
      this.isWorkflowOpen = !this.isWorkflowOpen
      this.isFormTypeOpen = false
      this.isSequenceTypeOpen = false
    },

    selectFormType (type) {
      if (this.isEdit) return // Prevent selection in edit mode
      this.formTypeselected = type
      this.isFormTypeOpen = false
      this.updateStoreData()
    },

    selectSequenceType (sequence) {
      this.selectedSequenceType = sequence
      this.sequenceTypeId = sequence.id
      this.isSequenceTypeOpen = false
      this.updateStoreData()
    },

    selectWorkflow (workflow) {
      this.selectedWorkflow = workflow
      this.workflowId = workflow.id
      this.isWorkflowOpen = false
      this.updateStoreData()
    },

    handleClickOutside (event) {
      if (!this.$el.contains(event.target)) {
        this.isFormTypeOpen = false
        this.isSequenceTypeOpen = false
        this.isWorkflowOpen = false
      }
    },

    handleSubmit () {
      // Skip name check in edit mode
      if (this.isEdit) {
        this.$emit('create-template', {
          formType: this.formTypeselected?.id,
          formTypeName: this.formTypeselected?.name,
          templateName: this.templateName,
          sequenceType: this.sequenceTypeId,
          workflowId: this.workflowId
        })
        return
      }

      // Check for duplicate name only in create mode
      formTemplateNameCheck(this.templateName).then(res => {
        if (res.core_form_templates.length) {
          alert('Template name already exists.')
          return 0
        } else {
          this.$emit('create-template', {
            formType: this.formTypeselected?.id,
            formTypeName: this.formTypeselected?.name,
            templateName: this.templateName,
            sequenceType: this.sequenceTypeId,
            workflowId: this.workflowId
          })
        }
      })
    }
  }
}
</script>

<style scoped>
/* Add disabled state styling */
.custom-select.disabled .select-trigger {
  background-color: #f3f4f6 !important;
  color: #9ca3af;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

.custom-select.disabled .select-trigger:hover {
  background-color: #f3f4f6 !important;
  border-color: #e5e7eb !important;
}

.lock-icon {
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

/* Rest of your existing styles stay the same */
.template-setup {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.template-box {
  width: 100%;
  max-width: 420px;
  min-width: 350px;
  padding: 8px 20px 16px 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.title {
  text-align: center;
  font-size: 1.25rem;
  font-weight: 600;
  margin-block-start: 6px;
  margin-block-end: 10px;
  color: #111827;
}

.description {
  text-align: center;
  font-size: 0.875rem;
  margin-top: 5px;
  margin-bottom: 15px;
  color: #6b7280;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 6px;
  /* color: #374151; */
}

.form-group input {
  width: 100%;
  padding: 10px 14px;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background-color: #f9fafb;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: white;
}

.custom-select {
  position: relative;
  width: 100%;
}

.select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 14px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #f9fafb;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.select-trigger:hover {
  border-color: #9ca3af;
  background-color: #f3f4f6;
}

.custom-select.is-open .select-trigger {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: white;
}

.placeholder {
  color: #9ca3af;
}

.dropdown-icon {
  width: 15px;
  height: 15px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.15s ease;
  border-bottom: 1px solid #f3f4f6;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.dropdown-item:active {
  background-color: #e5e7eb;
}

.item-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  color: #6b7280;
  flex-shrink: 0;
}

.dropdown-item:hover .item-icon {
  color: #374151;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-black {
  background-color: #111827;
  color: white;
}

.btn-black:hover:not(:disabled) {
  background-color: #1f2937;
  transform: translateY(-1px);
}

.btn-black:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.required-asterisk {
  color: #ef4444;
  margin-left: 2px;
}

/* Scrollbar styling for dropdown */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 6px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
