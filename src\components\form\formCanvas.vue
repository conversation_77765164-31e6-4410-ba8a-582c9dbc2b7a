<template>
  <div class="form-canvas">
    <!-- Sidebar -->
  <div class="sidebar">
  <div class="section">
  <h3>Fixed Fields</h3>
  <div class="line"></div>
  <div class="fixed-fields-container">
    <div
      v-for="(field, index) in fixedFields"
      :key="field.id || index"
      class="fixed-field-item pointer"
      :class="{ 'dragging': draggedFixedIndex === index }"
      @dragover.prevent
      @dragenter.prevent
      @drop="handleFixedDrop(index, $event)"
    >
      <!-- Drag Handle for Fixed Fields -->
      <div
        class="drag-handle"
        draggable="true"
        @dragstart="handleFixedDragStart(index, $event)"
        @dragend="handleFixedDragEnd"
      >
        <img
          src="~@/assets/images/icons/drag-handle.svg"
          alt="drag handle"
          class="drag-icon"
          style="width: 12px; height: 12px; opacity: 0.5; cursor: grab;"
        />
      </div>

      <div class="field-left">
        <img
          v-if="typeNameById[field.field_type_id] && iconMap[typeNameById[field.field_type_id]]"
          :src="iconMap[typeNameById[field.field_type_id]]"
          alt="field icon"
          class="field-icon"
        />
        <span v-overflow-tooltip class="field-name">{{ field.caption }}</span>
      </div>
    </div>
  </div>
</div>

<div class="section custom-fields-section">
  <h3>Custom Fields</h3>
  <div class="custom-fields-scroll-wrapper">
    <div class="custom-fields-container">
      <div
        v-for="(field, index) in customFields"
        :key="field.id || index"
        class="custom-field-item pointer"
        :class="{ 'dragging': draggedCustomIndex === index }"
        @dragover.prevent
        @dragenter.prevent
        @drop="handleCustomDrop(index, $event)"
      >
        <!-- Drag Handle for Custom Fields -->
        <div
          class="drag-handle"
          draggable="true"
          @dragstart="handleCustomDragStart(index, $event)"
          @dragend="handleCustomDragEnd"
        >
          <img
            src="~@/assets/images/icons/drag-handle.svg"
            alt="drag handle"
            class="drag-icon"
            style="width: 12px; height: 12px; opacity: 0.5; cursor: grab;"
          />
        </div>

        <div class="field-left" @click="enableEdit(index, getFieldName(field))">
          <img
           :src="iconMap[getFieldKey(field)]"
            alt=""
            class="field-icon"
          />
          <span v-if="editingFieldIndex !== index" class="field-name pointer">
            {{ getFieldName(field) }}
          </span>
          <input
            v-else
            v-model="editingFieldName"
            @keyup.enter="saveFieldName(index)"
            @blur="saveFieldName(index)"
            class="edit-input"
          />
        </div>
        <img
          v-if="editingFieldIndex === null"
          src="~@/assets/images/trash-2.svg"
          class="delete-icon"
          @click="deleteCustomField(index)"
        />
      </div>
    </div>
  </div>
  <button v-if="editingFieldIndex === null" class="add-field-btn" @click="showFieldModal = true">+ Add Field</button>
</div>
      <FormTemplateModal
        :customFields="customFields"
        :fields="customFormFields"
        :visible="showFieldModal"
        @add-field="handleAddCustomField"
        @close="showFieldModal = false"
      />
    </div>

<!-- Right Preview -->
 <div class="preview-wrapper">
   <div class="preview">
     <h2 v-if="!preview" class="template-name center">{{ formData.templateName }}</h2>
     <label v-if="!preview" class="form-type-name center" for="">{{ formData.formTypeName }}</label>

     <div class="preview-scroll-container">
       <div class="preview-fields-wrapper">
            <div v-if="[...fixedFields, ...customFields].length === 0" class="no-fields-message">
      Add a Custom Field
    </div>
       <div
         v-for="(field, index) in [...fixedFields, ...customFields]"
         :key="index"
         class="preview-field"
       >
         <div class="field-header">
           <div style="display: flex; align-items: center; gap: 8px;">
             <span class="field-index">{{ (index + 1).toString().padStart(2, '0') }}</span>
             <span class="field-title">{{ field.caption }}</span>
             <span v-if="field.required" class="required-asterisk">*</span>
           </div>

<template v-if="getFieldKey(field) === 'CONFIGURATION_LIST'">
  <div class="options-button" @click="openConfigurationListSelector(field, index)">
    <img
      src="~@/assets/images/settings.svg"
      alt="Settings"
      class="settings-icon"
    />
    <span class="options-label">Options</span>
  </div>
</template>
         </div>

         <div class="field-content">
           <input
             class="placeholder-input"
             :placeholder="`Select ${field.caption?.toLowerCase()} (Placeholder)`"
             :value="getFieldValue(field)" disabled
           />

           <div class="toggles">
             <div class="toggle-wrapper">
               <label class="switch">
                 <input type="checkbox" v-model="field.visibility" />
                 <span class="slider round"></span>
               </label>
               <span class="toggle-label">Visibility</span>
             </div>
             <div class="toggle-wrapper">
               <label class="switch">
                 <input type="checkbox" v-model="field.required" />
                 <span class="slider round"></span>
               </label>
               <span class="toggle-label">Required</span>
             </div>
           </div>
         </div>
       </div>
       </div>
     </div>
   <div class="center">
     <button @click="saveTemplate" class="btn btn-black" :disabled="[...fixedFields, ...customFields].length === 0">
       <img src="~@/assets/images/icons/save.svg" alt="Save" class="button-icon pointer" />
       Save Template
     </button>
   </div>
   </div>
 </div>

    <modal
    :title="'Choose Custom List'"
    :open="openConfig"
    @close="openConfig = false"
    >

<div class="dropdown-container">
  <!-- Dropdown -->
  <div class="field-group">
    <label for="customListSelect">Select Custom List :</label>
    <select
      id="customListSelect"
      class="custom-select"
      v-model="selectedList"
    >
      <option disabled value="">-- Select a list --</option>
      <option
        v-for="list in customLists"
        :key="list.id"
        :value="list"
      >
        {{ list.name }}
      </option>
    </select>
  </div>

  <!-- Buttons -->
  <div class="actions">
    <button class="btn-2 btn-2-cancel" @click="cancelConfig">Cancel</button>
    <button
      class="btn-2 btn-2-confirm"
      :class="{ 'btn-2-confirm-active': selectedList }"
      @click="confirmConfig"
    >
      Confirm
    </button>
  </div>
</div>

    </modal>
  </div>
</template>

<script>
import { getFormFields, GetActiveCustomLists } from '@/api'
import config from '@/config'
import modal from '../common/modal.vue'
import FormTemplateModal from '@/components/form/formTemplateModal.vue'
import { alert } from '@/plugins/notification'
import { iconMap } from '@/utils/formFieldIcons'

export default {
  name: 'FormCanvas',
  components: { FormTemplateModal, modal },
  props: {
    formData: {
      type: Object,
      required: true
    },
    fixedFields: {
      type: Array,
      required: true
    },
    customFields: {
      type: Array,
      required: true
    },
    preview: {
      type: Boolean
    }
  },
  data () {
    return {
      editingFieldIndex: null,
      editingFieldName: '',
      types: config.FORM_TYPE,
      selectedList: null,
      iconMap,
      selected: null,
      openConfig: false,
      customLists: [],
      customFormFields: [],
      showFieldModal: false,
      // Drag and drop state for custom fields
      draggedCustomIndex: null,
      draggedCustomField: null,
      // Drag and drop state for fixed fields
      draggedFixedIndex: null,
      draggedFixedField: null
    }
  },
  computed: {
    typeNameById () {
      return Object.fromEntries(
        Object.entries(this.types).map(([key, value]) => [value, key])
      )
    },
    allFields () {
      return [...this.fixedFields, ...this.customFields]
    },
    visibleFieldsCount () {
      return this.allFields.filter(field => field.visibility).length
    }
  },
  mounted () {
    GetActiveCustomLists().then(res => {
      this.customLists = res.core_custom_list.map(list => {
        return {
          id: list.id,
          name: list.name
        }
      })
    })
    getFormFields().then(res => {
      if (Array.isArray(res.core_form_fields)) {
        this.customFormFields = res.core_form_fields.filter(field => field.enabled === true).map(field => ({
          caption: field.caption,
          id: field.id,
          key: field.key
        }))
      } else {
        this.customFormFields = []
      }
    })
  },
  methods: {
    getFieldValue (field) {
      if (this.getFieldKey(field) === 'CONFIGURATION_LIST') {
        // If custom_list_name exists, use it
        if (field.custom_list_name) {
          return field.custom_list_name
        }
        // If only custom_list_id exists, find the name from customLists
        if (field.custom_list_id && this.customLists.length) {
          const matchedList = this.customLists.find(list => list.id === field.custom_list_id)
          return matchedList ? matchedList.name : ''
        }
      }
      return ''
    },
    getFieldKey (field) {
    // For editing: key is inside form_field object
      if (field.form_field && field.form_field.key) {
        return field.form_field.key
      }
      // For creation: key is directly on field object
      return field.key
    },
    getFieldName (field) {
    // Priority: name > caption
      return field.caption || field.field_name
    },
    // Custom Fields drag and drop methods
    handleCustomDragStart (index, event) {
      this.draggedCustomIndex = index
      this.draggedCustomField = this.customFields[index]
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/html', event.target.outerHTML)

      // Add visual feedback
      setTimeout(() => {
        event.target.style.opacity = '0.5'
      }, 0)
    },

    handleCustomDragEnd (event) {
      event.target.style.opacity = '1'
      this.draggedCustomIndex = null
      this.draggedCustomField = null
    },

    handleCustomDrop (dropIndex, event) {
      event.preventDefault()

      if (this.draggedCustomIndex === null || this.draggedCustomIndex === dropIndex) {
        return
      }

      // Create a copy of customFields array
      const newCustomFields = [...this.customFields]

      // Remove the dragged field from its original position
      const draggedField = newCustomFields.splice(this.draggedCustomIndex, 1)[0]

      // Insert the dragged field at the new position
      newCustomFields.splice(dropIndex, 0, draggedField)

      // Update the customFields array
      this.customFields.splice(0, this.customFields.length, ...newCustomFields)
    },

    // Fixed Fields drag and drop methods
    handleFixedDragStart (index, event) {
      this.draggedFixedIndex = index
      this.draggedFixedField = this.fixedFields[index]
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/html', event.target.outerHTML)

      // Add visual feedback
      setTimeout(() => {
        event.target.style.opacity = '0.5'
      }, 0)
    },

    handleFixedDragEnd (event) {
      event.target.style.opacity = '1'
      this.draggedFixedIndex = null
      this.draggedFixedField = null
    },

    handleFixedDrop (dropIndex, event) {
      event.preventDefault()

      if (this.draggedFixedIndex === null || this.draggedFixedIndex === dropIndex) {
        return
      }

      // Emit event to parent to handle fixed fields reordering
      // since fixedFields is a prop, we need the parent to handle the update
      this.$emit('reorder-fixed-fields', {
        fromIndex: this.draggedFixedIndex,
        toIndex: dropIndex
      })
    },

    saveTemplate () {
      const allFields = [...this.fixedFields, ...this.customFields]
      const configListFields = allFields.filter(field => this.getFieldKey(field) === 'CONFIGURATION_LIST')
      const unselectedConfigFields = configListFields.filter(field => !field.custom_list_id)

      if (unselectedConfigFields.length > 0) {
        alert('Please select a custom list for all Configuration List fields before saving')
        return
      }
      console.log(allFields, 'these are all the fields')
      const transformedFields = allFields.map(field => ({
        autogenerated: field.autogenerated,
        caption: field.caption,
        field_name: (field.name || field.caption).replace(/\s+/g, '_').toLowerCase(),
        field_type_id: this.getFieldTypeId(field),
        fixed: !!this.fixedFields.includes(field),
        required: !!field.required,
        visibility: !!field.visibility,
        field_id: field.field_id
      }))
      const visibleFieldsCount = transformedFields.filter(f => f.visibility).length

      if (visibleFieldsCount < 2) {
        alert('At least 2 fields should have visibility')
        return
      } else if (visibleFieldsCount > 2) {
        alert('Only 2 fields should have visibility, not more')
        return
      }

      this.$emit('save-template', transformedFields)
    },
    enableEdit (index, name) {
      this.editingFieldIndex = index
      this.editingFieldName = name
      this.$nextTick(() => {
      // Auto-focus the input
        const input = this.$el.querySelectorAll('.edit-input')[index]
        if (input) input.focus()
      })
    },
    saveFieldName (index) {
      if (this.editingFieldName.trim()) {
        this.customFields[index].caption = this.editingFieldName.trim()
      }
      this.editingFieldIndex = null
      this.editingFieldName = ''
    },
    openConfigurationListSelector (field, index) {
      this.selected = index
      // Check if field has existing custom_list_id and set selectedList
      if (field.custom_list_id) {
        const existingList = this.customLists.find(list => list.id === field.custom_list_id)
        this.selectedList = existingList || null
      } else {
        this.selectedList = null
      }

      this.openConfig = true
    },
    confirmConfig () {
      if (this.selectedList) {
        const allFields = [...this.fixedFields, ...this.customFields]
        allFields[this.selected].custom_list_id = this.selectedList.id
        allFields[this.selected].custom_list_name = this.selectedList.name
        this.openConfig = false
        this.selectedList = null
        this.selected = null
      } else alert('Choose a custom list')
    },
    cancelConfig () {
      this.selected = null
      this.selectedList = null
      this.openConfig = false
    },
    getFieldTypeId (field) {
      if (field.field_type_id) {
        return field.field_type_id
      }
      const fieldKey = this.getFieldKey(field)
      const match = this.customFormFields.find(f => f.caption === field.caption || f.key === fieldKey)
      return match ? match.id : null
    },
    handleAddCustomField (field) {
      const isDuplicate = this.customFields.some(f => this.getFieldName(f) === field.name)
      if (isDuplicate) {
        alert('Field with same name already exists.')
      } else {
        this.customFields.push({
          autogenerated: false,
          caption: field.name,
          key: field.key,
          name: field.name,
          visibility: false,
          // field_id,
          required: false
        })
        this.showFieldModal = false
      }
    },
    deleteCustomField (index) {
      this.customFields.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.sort {
  height: 2em;
  width: 10rem;
  margin: 0 5px 0 5px;
  font-size: 14px;
  background-color: var(--brand-light-color);
  line-height: 1;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  padding: 4px 12px;
}

.form-canvas {
  display: flex;
  background-color: #fff;
  height: 100%;
}
  .sidebar {
    grid-template-rows: 50% 50%;
    width: 280px;
    border-right: 1px solid #e0e0e0;
    padding: 0;
    margin: 0;
    display: grid;
    overflow-y: auto;
    overflow: hidden;
}
.section {
  padding: 20px 10px 0; // Add padding back to sections

  &:first-child {
    padding-top: 14px; // First section top padding
  }

  &:last-child {
    flex: 1; // Make last section take remaining space
    display: flex;
    flex-direction: column;
    overflow: hidden; // Prevent section overflow
    padding-bottom: 20px; // Bottom padding for last section
  }
}

.section h3 {
  font-size: 13px;
  font-weight: 400;
  color: #4a4a4a;
  margin-bottom: 12px;
}

.field-item {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 20px;
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 6px;
  transition: background-color 0.3s;
}

.field-item:hover {
  background-color: #f0f0f0;
}

.field-name {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 150px;
}

.delete-icon {
  width: 12px;
  height: 12px;
  cursor: pointer;
}

.add-field-btn {
  display: flex;
  align-items: center;
  // gap: 8px;
  width: 100%;
  padding: 10px;
  background-color: transparent;
  border: none;
  font-size: 13px;
  font-weight: 500;
  color: #555;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
  flex-shrink: 0; // Prevent shrinking

  &:hover {
    background-color: #f0f0f0;
    color: black;
    font-weight: 400;
  }
}

.preview-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
}

.preview {
  width: 100%;
  max-width: 900px;
  display: flex;
  overflow-y: auto;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.preview-scroll-container {
  overflow-y: auto;
  width: 100%;
  max-height: calc(100vh - 180px);
  margin-bottom: 16px;
  padding-right: 6px;
}

.preview-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.preview-scroll-container::-webkit-scrollbar-thumb {
  border-radius: 4px;
}

.template-name {
  font-size: 24px;
  margin-bottom: 10px;
  font-weight: 500;
}

.form-type-name {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 10px;
}

.preview-field {
  border-radius: 10px;
  padding: 15px;
  background-color: #fff;
}

.field-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 10px;
}

.field-index {
  font-size: 11px;
  color: var(--brand-color);
  background-color: transparent;
  border: 1px solid var(--brand-light-color);
  border-radius: 6px;
  padding: 2px 6px;
}

.field-title {
  font-size: 13px;
  font-weight: 500;
}

.field-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.placeholder-input {
  flex: 1;
  width: 150px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #777;
}

.toggles {
  display: flex;
  gap: 20px;
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
}

.toggle-label {
  font-size: 12px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 28px;
  height: 16px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #aaa;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: '';
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4a4a4a;
}

input:checked + .slider:before {
  transform: translateX(12px);
}

.save-btn {
  margin-top: 20px;
  padding: 10px 20px;
  font-weight: 600;
  background-color: #616161;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.field-icon {
  width: 10px;
  height: 10px;
}

// Fixed fields container with scroll
.fixed-fields-container {
  max-height: 70%;
  overflow-y: auto;
  padding-right: 4px; // Space for scrollbar
}

.fixed-fields-container::-webkit-scrollbar {
  width: 4px;
}

.fixed-fields-container::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.fixed-fields-container::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

// Custom fields container with scroll
.custom-fields-container {
  overflow-y: auto;
  max-height: 20vh;
  margin-bottom: 5px;
  padding-right: 4px;
}

.custom-fields-container::-webkit-scrollbar {
  width: 4px;
}

.custom-fields-container::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.custom-fields-container::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

// Fixed field item styles for drag and drop
.fixed-field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  gap: 10px;
  transition: all 0.2s ease;
  user-select: none;
  margin-bottom: 6px;

  &:hover {
    background-color: #f8f9fa;
  }

  &.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
}

// Custom field item styles for drag and drop
.custom-field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  gap: 10px;
  transition: all 0.2s ease;
  user-select: none;
  margin-bottom: 6px;

  &:hover {
    background-color: #f8f9fa;
  }

  &.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
}

.drag-handle {
  display: flex;
  align-items: center;
  padding: 2px;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.drag-icon {
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

.fixed-field-item:hover .drag-icon,
.custom-field-item:hover .drag-icon {
  opacity: 0.8;
}

.field-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.line {
  height: 1px;
  background-color: #ccc;
  margin: 12px -10px; // Remove negative margins
}

.edit-input {
  font-size: 14px;
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 0px;
  margin-bottom: 3px;
  vertical-align: middle;
}

.required-asterisk {
  color: red;
  margin-left: 0px;
}

.dropdown-container {
  padding: 4px 0;
}

.field-group {
  margin-bottom: 24px;
}

.field-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.custom-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f9fafb;
  appearance: none;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
  box-sizing: border-box;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.custom-select:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: white;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.btn-2 {
  flex: 1;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-2-cancel {
  background-color: #f8f9fa;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.btn-2-cancel:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.btn-2-confirm {
  background-color: #9ca3af;
  color: white;
}

.btn-2-confirm-active {
  background-color: #1f2937;
}

.btn-2-confirm-active:hover {
  background-color: #111827;
}

.btn-2:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.dropdown label {
  margin-top: 5px;
  font-weight: 400;
  font-size: 14px;
}

.config-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.options-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.options-button:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.settings-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.options-button:hover .settings-icon {
  opacity: 1;
}

.options-label {
  font-size: 12px;
  font-weight: 400;
  color: #495057;
  transition: color 0.2s ease;
}

.options-button:hover .options-label {
  color: #343a40;
}
.no-fields-message {
  text-align: center;
  padding: 20px 10px;
  font-size: 14px;
  font-style: italic;
  border-radius: 6px;
}
</style>
