<!-- Updated form-builder component with persistent step data -->
<template>
  <div class="fh fw center" v-if="loading">
    <loading-circle />
  </div>
  <div v-else class="form-builder">
    <div class="header-container">
      <div class="header-content">
        <img
          src="~@/assets/images/icons/arrow-back.svg"
          alt="Back"
          class="back-button pointer"
          @click="goToPrevious"
        />

        <div class="stepper-header-container" v-if="!isPreview">
          <StepperHeader
            :step="activeStep"
            @step-click="handleStepChange"
            :isPreview="isPreview"
          />
        </div>

        <div class="preview-title" v-if="isPreview">
          <div class="preview-header-text">
            <h2 class="template-name">{{ formData.templateName }}</h2>
            <p class="form-type-name">{{ formData.formTypeName }}</p>
          </div>
        </div>

        <!-- Empty div for flex balance -->
        <div class="header-spacer"></div>
      </div>
    </div>

    <div class="header-divider"></div>

    <!-- Preview Mode - Show only FormCanvas -->
    <div v-if="isPreview" class="content-container full-width">
      <FormCanvas
        :isEdit="false"
        :preview="isPreview"
        :formData="formData"
        :fixedFields="fixedFields"
        :customFields="customFields"
        :autogeneratedFields="autoGeneratedFields"
        @add-custom-field="customFields.push($event)"
        @save-template="handleSaveTemplate"
        @reorder-fixed-fields="handleReorderFixedFields"
      />
    </div>

<!-- Create/Edit Mode - Show step transitions -->
<keep-alive v-else>
  <transition name="slide-fade" mode="out-in">
    <div
      v-if="activeStep === 'Basic Info'"
      key="basic-info"
      class="content-container centered full-width"
      style="padding-top: 20px;"
    >
      <FormTemplateSetup
        :isEdit="isEdit"
        :editData="formData"
        :workflows="workflowTempaltes"
        :sequenceTemplates="formSequenceTemplates"
        :filteredFormTypeList="filteredFormTypeList"
        @create-template="handleCreateTemplate"
        @change-form-type="changeFormTypeInFormData"
      />
    </div>

    <div
      v-else-if="activeStep === 'Customise Fields'"
      key="customise-fields"
      class="content-container full-width"
    >
      <FormCanvas
        :isEdit="isEdit"
        :isPreview="false"
        :preview="isPreview"
        :formData="formData"
        :fixedFields="fixedFields"
        :customFields="customFields"
        :autogeneratedFields="autoGeneratedFields"
        @add-custom-field="customFields.push($event)"
        @save-template="handleSaveTemplate"
        @reorder-fixed-fields="handleReorderFixedFields"
      />
    </div>

    <div
      v-else-if="activeStep === 'Completed'"
      key="completed"
      class="content-container centered"
    >
      <div class="success-screen">
        <div class="check-icon">
          <img src="~@/assets/images/circle-check.svg" style="width: 80px; height: 80px;" alt="Success" />
        </div>
        <div class="success-message">
          <h2 v-if="isEdit">Template Updated Successfully!</h2>
          <h2 v-else>Template Created Successfully!</h2>
          <p>Your form template "<strong>{{ formData.templateName }}</strong>" has been saved.</p>
          <p class="sub">Redirecting to template list...</p>
        </div>
      </div>
    </div>
  </transition>
</keep-alive>
  </div>
</template>

<script>
import StepperHeader from '@/components/form/stepsHeader.vue'
import loadingCircle from '@/components/common/loadingCircle.vue'
import FormTemplateSetup from '@/components/form/formTemplateSetup.vue'
import FormCanvas from '@/components/form/formCanvas.vue'
import { mapGetters } from 'vuex'
import { getFullWorkFLowTemplates, GetTemplateData, getFormTypeList, createForm, getDetailFormTemplate, updateForm } from '@/api'
import { alert } from '@/plugins/notification'
import config from '@/config'

export default {
  components: {
    FormTemplateSetup,
    StepperHeader,
    FormCanvas,
    loadingCircle
  },
  data () {
    return {
      id: null,
      editInfo: null,
      isEdit: false,
      isPreview: false,
      loading: false,
      config: config,
      formTypeList: [],
      formSequenceTemplates: [],
      workflowTempaltes: [],
      autoGeneratedFields: [],
      WFTLoading: false,
      formData: {
        formTypeName: '',
        templateName: '',
        formType: '',
        selectedWorkflow: {},
        sequence_template_id: null
      },
      activeStep: 'Basic Info',
      fixedFields: [],
      customFields: [],
      step: 'Upcoming',
      templateCreated: false,
      formType: null,
      templateName: '',
      showAddFieldModal: false
    }
  },
  computed: {
    ...mapGetters('form', [
      'formFields',
      'formTemplateBody',
      'selectedFormElementToEdit',
      'preventElementSelection',
      'formTemplateName',
      'formTemplateType',
      'linkedWFTemplate',
      'linkedFormSequenceId',
      'setupData'
    ]),
    filteredFormTypeList () {
      return this.formTypeList.filter(type =>
        type.id !== this.config.STANDARD_MATERIAL_FORM.form_type &&
        type.id !== this.config.STANDARD_BOM_FORM.form_type
      )
    }
  },
  mounted () {
    getFormTypeList().then(res => {
      this.formTypeList = res?.core_form_types
    })
  },
  methods: {
    // Clear setup data when leaving the form builder
    clearSetupData () {
      this.$store.dispatch('form/clearSetupData')
    },

    goToPrevious () {
      if (this.isPreview) {
        // Navigate back to form templates list from preview
        this.$router.push('/settings/copy-forms')
      } else if (this.activeStep === 'Customise Fields') {
        this.activeStep = 'Basic Info'
      } else if (this.activeStep === 'Basic Info') {
        // Clear setup data when leaving form builder
        this.clearSetupData()
        this.$router.push('/settings/copy-forms')
      }
    },
    handleSaveTemplate (fields) {
      // Disable save functionality in preview mode
      if (this.isPreview) {
        alert('Template cannot be modified in preview mode')
        return
      }

      if (!this.isEdit) {
        createForm(fields, this.formData.formType, this.formData.templateName, this.formData.workflowId, this.formData.sequence_template_id)
          .then(res => {
            if (res) {
              this.activeStep = 'Completed'
              setTimeout(() => {
                this.$router.push('/settings/copy-forms')
              }, 2000)
            }
          })
          .catch(err => {
            alert('Something went wrong')
            console.error('Error while creating form template:', err)
          })
      } else {
        updateForm(this.id, fields, this.formData.templateName, this.formData.workflowId, this.formData.sequence_template_id)
          .then(res => {
            if (res) {
              this.activeStep = 'Completed'
              setTimeout(() => {
                this.$router.push('/settings/copy-forms')
              }, 2000)
            }
          })
          .catch(err => {
            alert('Something went wrong')
            console.error('Error while creating form template:', err)
          })
      }
    },
    handleReorderFixedFields (event) {
      // Disable reordering in preview mode
      if (this.isPreview) return

      const { fromIndex, toIndex } = event
      const newFixedFields = [...this.fixedFields]
      const draggedField = newFixedFields.splice(fromIndex, 1)[0]
      newFixedFields.splice(toIndex, 0, draggedField)
      this.fixedFields = newFixedFields
    },
    changeFormTypeInFormData (event) {
      this.formData.formType = event.target.value
      this.changeFormType()
    },
    async getTemplatesForWFL () {
      try {
        this.WFTLoading = true
        this.workflowTempaltes = []
        const res = await getFullWorkFLowTemplates(config.CORE_FEATURES.FORMS)
        this.workflowTempaltes = res.workflow_templates
        if (res) {
          GetTemplateData().then(res => {
            const templates = res?.core_sequence_id_template || []

            this.formSequenceTemplates = templates.filter(
              template => template.core_feature?.id === 5
            )
          })
        }
        this.WFTLoading = false
      } catch (error) {
        alert('Error fetching  form templates')
        this.WFTLoading = false
        console.error('Error fetching templates', error)
      }
    },
    changeFormType () {
      const formDefaultFields = (
        this.formTypeList.find((type) => type.id === this.formData.formType)
          .default_fields || []
      ).map((type) => {
        const key = this.formFields.find(
          (field) => field.id === type.field_type_id
        ).key
        return {
          ...type,
          key
        }
      })
      this.$store.commit(
        'form/setFormTemplateType',
        this.formData.formType
      )
      this.$store.dispatch('form/setSelectedFormElementToEdit', null)
      this.$store.dispatch('form/removeTemplateElementFormTemplateBody')
      this.$store.dispatch(
        'form/addTemplateElementToTemplateBody',
        formDefaultFields
      )
    },
    handleStepChange (step) {
      // Disable step change in preview mode
      if (this.isPreview) return
      this.activeStep = step
    },
    addCustomField (newField) {
      // Disable adding custom fields in preview mode
      if (this.isPreview) return
      this.customFields.push(newField)
    },
    handleCreateTemplate ({ formType, templateName, workflowId, sequenceType, formTypeName }) {
      this.templateCreated = true
      this.formData.formType = formType
      this.formData.templateName = templateName
      this.formData.workflowId = workflowId
      this.formData.formTypeName = formTypeName
      this.formData.sequence_template_id = sequenceType

      this.activeStep = 'Customise Fields'
      this.fixedFields = this.getPredefinedFields(formType)
    },
    getPredefinedFields (typeId) {
      const selectedType = this.formTypeList.find(t => t.id === typeId)
      if (!selectedType) return []
      this.autoGeneratedFields = selectedType.default_fields.filter(field => field.autogenerated === true)
      return selectedType.default_fields.filter(field => field.autogenerated === false)
    },
    // Load template data for both edit and preview modes
    async loadTemplateData (id) {
      this.loading = true
      try {
        const res = await getDetailFormTemplate(id)
        const values = res.core_form_templates_by_pk

        // Handle both fixed and custom fields
        const allTemplateFields = values.template_versions[0].template_fields || []

        this.customFields = allTemplateFields
          .filter(field => field.fixed === false)
          .map(field => ({
            ...field,
            name: field.field_name
          }))

        this.fixedFields = allTemplateFields
          .filter(field => field.fixed === true)
          .map(field => ({
            ...field,
            name: field.field_name
          }))

        // Set form data
        this.formData.templateName = values?.name
        this.formData.formType = values.core_form_type.id
        this.formData.formTypeName = values.core_form_type.name
        this.formData.sequence_template_id = values?.core_sequence_id_template?.id
        this.formData.workflowId = values?.workflow_template_id

        // Get auto-generated fields for the form type
        this.getPredefinedFields(values.core_form_type.id)
      } catch (err) {
        console.error('Error loading template data:', err)
        alert('Error loading template data')
      } finally {
        this.loading = false
      }
    }
  },
  created () {
    const id = this.$route.params.id
    const currentPath = this.$route.path

    // Determine mode based on route path
    this.isPreview = currentPath.includes('/form-preview/')
    this.isEdit = currentPath.includes('/form-editor/')

    if (id && (this.isEdit || this.isPreview)) {
      this.id = id
      this.loadTemplateData(id)
    }

    // Only load workflow templates for create/edit modes
    if (!this.isPreview) {
      this.getTemplatesForWFL()
    }
  }
}
</script>

<style scoped>
.form-builder {
  height: calc(100vh - 84px);
  width: 100%;
  display: flex;
  gap: 0px;
  flex-direction: column;
  overflow: hidden;
}

.header-container {
  width: 100%;
  padding: 0 20px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 0;
  margin-bottom: 20px;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  flex-shrink: 0;
}

.stepper-header-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.preview-title {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-header-text {
  text-align: center;
}

.preview-header-text .template-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.preview-header-text .form-type-name {
  font-size: 0.875rem;
  font-weight: 400;
  margin: 0;
  line-height: 1.2;
}

.header-spacer {
  width: 24px;
  flex-shrink: 0;
}

.header-divider {
  height: 1px;
  background-color: #e0e0e0;
  flex-shrink: 0;
}

.content-container {
  flex: 1;
  width: 100%;
  height: 70%;
  overflow: hidden;
  position: relative;
}

.content-container.centered {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.content-container.full-width {
  /* display: flex; */
  width: 100%;
}

.success-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  padding: 40px 20px;
}

.check-icon {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon img {
  width: 64px !important;
  height: 64px !important;
  background-color: #10b981;
  border-radius: 50%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.success-message {
  max-width: 400px;
}

.success-message h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #111827;
  line-height: 1.2;
}

.success-message p {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.4;
}

.success-message p strong {
  color: #374151;
  font-weight: 500;
}

.success-message .sub {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 16px;
  margin-bottom: 0;
}

/* Smooth slide transition */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s ease-in-out;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(50px);
}

.slide-fade-enter-to {
  opacity: 1;
  transform: translateX(0);
}

.slide-fade-leave-from {
  opacity: 1;
  transform: translateX(0);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-50px);
}

/* Old fade-slide transition (keeping for backward compatibility) */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 1.5s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Responsive styles for mobile */
@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
    gap: 10px;
  }

  .stepper-header-container {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }

  .preview-title {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }

  .preview-header-text .template-name {
    font-size: 1.25rem;
  }

  .preview-header-text .form-type-name {
    font-size: 0.8rem;
  }

  .header-spacer {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 15px;
  }

  .preview-header-text .template-name {
    font-size: 1.1rem;
  }

  .preview-header-text .form-type-name {
    font-size: 0.75rem;
  }

  .back-button {
    width: 20px;
    height: 20px;
  }
}
</style>
